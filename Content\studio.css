﻿body {
    color: white;
    background-color: black
}

#canvas {
    left: 0;
    top: 0;
    width: 200px;
    height: 50px;
}

#volume-visualizer {
    --volume: 0%;
    position: relative;
    width: 200px;
    height: 20px;
    /*margin: 50px;*/
    background-color: #DDD;
}

    #volume-visualizer::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: var(--volume);
        background-color: green;
        transition: width 100ms linear;
    }

#rec-tool-volume-visualizer {
    --volume: 0%;
    position: relative;
    width: 200px;
    height: 20px;
    /*margin: 50px;*/
    background-color: #DDD;
}

    #rec-tool-volume-visualizer::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: var(--volume);
        background-color: green;
        transition: width 100ms linear;
    }

.track_stack_item {
    color: white;
    height: 30px;
    width: 100%;
    background-color: grey;
    margin: 5px
}

.track_stack_item_red {
    color: white;
    height: 30px;
    width: 100%;
    background-color: red;
    margin: 5px
}
