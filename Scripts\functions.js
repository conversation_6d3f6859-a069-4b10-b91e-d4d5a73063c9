﻿function recToolInit() {

    recToolCurrentTrackIndex = 0;

    //set 1st and 2nd track in "img_current" and "img_next"

    document.getElementById("img_current").src = URL.createObjectURL(tracks[0].coverArt);
    try {
        document.getElementById("img_next").src = URL.createObjectURL(tracks[1].coverArt);
    }
    catch (e) {

    }

    //add first track to "rec_tool_audio_player"
    document.getElementById("rec_tool_audio_player_src").src = URL.createObjectURL(tracks[0].trackAudio);
    document.getElementById("rec_tool_audio_player").load();
    visualizerLoad();
    document.getElementById("rec_tool_audio_player").currentTime = tracks[0].startingPosOfTrack;
    currentTrackAudioFileStartTime = tracks[0].startingPosOfTrack;

    //add tracks to "track_stack"
    var track_stack_inner_html = "";
    for (var i = 0; i < tracks.length; i++) {
        if (i == 0) {

            track_stack_inner_html += `<div class='track_stack_item_red'>${tracks[i].trackTitle}</div>`;
        }
        else {
            track_stack_inner_html += `<div class='track_stack_item'>${tracks[i].trackTitle}</div>`;
        }
    }

    document.getElementById("track_stack").innerHTML = track_stack_inner_html;

}


function visualizerLoad() {

    var audio = document.getElementById("rec_tool_audio_player");

    var context = new AudioContext();
    var src = context.createMediaElementSource(audio);
    var analyser = context.createAnalyser();

    var canvas = document.getElementById("canvas");
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    var ctx = canvas.getContext("2d");

    src.connect(analyser);
    analyser.connect(context.destination);

    analyser.fftSize = 256;

    var bufferLength = analyser.frequencyBinCount;
    console.log(bufferLength);

    var dataArray = new Uint8Array(bufferLength);

    var WIDTH = canvas.width;
    var HEIGHT = canvas.height;

    var barWidth = (WIDTH / bufferLength) * 2.5;
    var barHeight;
    var x = 0;

    function renderFrame() {
        requestAnimationFrame(renderFrame);

        x = 0;

        analyser.getByteFrequencyData(dataArray);

        ctx.fillStyle = "#000";
        ctx.fillRect(0, 0, WIDTH, HEIGHT);

        for (var i = 0; i < bufferLength; i++) {
            barHeight = dataArray[i];

            var r = barHeight + (25 * (i / bufferLength));
            var g = 250 * (i / bufferLength);
            var b = 50;

            ctx.fillStyle = "rgb(" + r + "," + g + "," + b + ")";
            ctx.fillRect(x, HEIGHT - barHeight, barWidth, barHeight);

            x += barWidth + 1;
        }
    }

    renderFrame();
}


function upload_track_audio_eventhandler() {
    var files = event.target.files;
    currentTrackAudioFile = files[0];
    console.log(currentTrackAudioFile);
    generateTrackDataInnerHtml();
    document.getElementById("src").setAttribute("src", URL.createObjectURL(files[0]));
    document.getElementById("audio").load();
    document.getElementById("upload_track_cover").addEventListener("change", handleCoverUpload, false);
    document.getElementById("btn_add_to_crate").addEventListener("click", addTrackToCrate);
    document.getElementById("audio").onseeked = seekedFunc;
}


function hideAllMainDivs() {

    document.getElementById("crate").style.display = "none";
    document.getElementById("settings").style.display = "none";
    document.getElementById("details").style.display = "none";
    document.getElementById("rec-tool").style.display = "none";

}



function menuButtonClicked(element) {

    if (element.id == "btn_create") {

        //hide all main divs
        hideAllMainDivs();
        document.getElementById("crate").style.display = "block";


    }
    else if (element.id == "btn_settings") {
        //hide all main divs
        hideAllMainDivs();
        document.getElementById("settings").style.display = "block";
    }
    else if (element.id == "btn_details") {
        //hide all main divs
        hideAllMainDivs();
        document.getElementById("details").style.display = "block";
    }
    else {
        //hide all main divs
        hideAllMainDivs();
        document.getElementById("rec-tool").style.display = "block";
        recToolInit();
    }

}


function handleAudioFileUpload() {
    var files = event.target.files;
    currentTrackAudioFile = files[0];
    console.log(currentTrackAudioFile);
}

function handleCoverUpload() {
    var files = event.target.files;
    currentTrackCoverFile = files[0];
    console.log(currentTrackCoverFile);
}


function addTrackToCrate() {

    //do validation cheks




    //create a Track object and add it to the "tracks" array
    var track = new Track();
    track.allowFreeDownload = document.getElementById("cb_allow_download").checked;
    track.ArtistName = document.getElementById("txt_artist_name").value;
    track.BPM = document.getElementById("txt_bpm").value;
    track.buyLink = document.getElementById("txt_buy_link").value;
    track.coverArt = currentTrackCoverFile;
    track.mainGenre = document.getElementById("txt_main_genre").value;
    track.RecordLabel = document.getElementById("txt_record_label").value;
    track.ReleaseDate = document.getElementById("txt_release_date").value;
    track.remixArtist = document.getElementById("txt_remix_artist").value;
    track.SpreakerNotes = document.getElementById("txt_speaker_notes").value;
    track.startingPosOfTrack = currentTrackAudioFileStartTime;
    track.StreamLink = document.getElementById("txt_stream_link").value;
    track.SubGenre = document.getElementById("txt_sub_genre").value;
    track.trackAudio = currentTrackAudioFile;
    track.trackTitle = document.getElementById("txt_track_title").value;

    //add the track to "tracks" array
    tracks[trackCount] = track;
    trackCount++;

    //update the "track_list" element 
    generateTrackListInnerHtml();

    document.getElementById("track_data").innerHTML = "";

}

function resetTrackData() {


}

function generateTrackListInnerHtml() {

    html = "<table>";
    //foreacy tracks (array)
    for (var i = 0; i < tracks.length; i++) {

        var trackNo = i + 1;

        html += "<tr>";
        html += `<td>Track ${trackNo}</td>`
        html += `<td>${tracks[i].trackTitle}</td>`
        html += `<td>${tracks[i].ArtistName}</td>`
        html += `<td>${tracks[i].RecordLabel}</td>`
        html += `<td>${tracks[i].trackTitle}</td>`
        html += "</tr>";
    }
    html += "</table>";

    document.getElementById("track_list").innerHTML = html;
}

function seekedFunc() {
    //pause
    document.getElementById("audio").pause();


    //update label
    var currentTimeInSecs = Math.floor(document.getElementById("audio").currentTime);
    currentTrackAudioFileStartTime = currentTimeInSecs;
    var mins = Math.floor(currentTimeInSecs / 60);
    var secs = currentTimeInSecs % 60;
    document.getElementById("lbl_track_start_time").innerHTML = `Start time = ${mins}.${secs}`;


}

function generateTrackDataInnerHtml() {
    html = `<table>
                        <tr>
                            <td>
                                <input id="txt_track_title" type="text" placeholder="Track Title" /></td>
                            <td>
                                <input id="txt_artist_name" type="text" placeholder="Artist Name" /></td>
                        </tr>
                        <tr>
                            <td>
                                <input id="txt_remix_artist" type="text" placeholder="Remix Artist" /></td>
                            <td>
                                <input id="txt_record_label" type="text" placeholder="Record Label" /></td>
                        </tr>
                        <tr>
                            <td>
                                <input id="txt_main_genre" type="text" placeholder="Main Genre" /></td>
                            <td>
                                <input id="txt_sub_genre" type="text" placeholder="Sub Genre" /></td>
                        </tr>
                        <tr>
                            <td>
                                <input id="txt_bpm" type="text" placeholder="BPM" /></td>
                            <td>
                                <input id="txt_release_date" type="text" placeholder="Release Date" /></td>
                        </tr>
                    </table>
                    <table>
                        <tr>
                            <td>
                                <div>Tracks Cover Art</div>
                                <input id='upload_track_cover' type='file' style='display:none' accept='.jpg,.jpeg' />
                                <div><input type="button" value="+" style="height:100px;width:100px" onclick='upload_track_cover.click()' /></div>
                            </td>
                            <td>
                                <div>
                                    <div>starting position of the track</div>
                                        <audio id="audio" controls>
                                            <source src="" id="src" />
                                        </audio>
                                        <label id="lbl_track_start_time" ></label>
                                </div>
                            </td>

                        </tr>
                        <tr>
                            <td colspan="2"><input type="text" id="txt_buy_link" style="width:100%" placeholder="Buy Link" /></td>
                        </tr>
                        <tr>
                            <td colspan="2"><input type="text" id="txt_stream_link" style="width:100%" placeholder="Stream Link" /></td>
                        </tr>
                        <tr>
                            <td colspan="2"><input type="checkbox" id="cb_allow_download"/><label>allow free download</label></td>
                        </tr>
                        <tr>
                            <td colspan="2"><textarea id="txt_speaker_notes" placeholder="Speaker Notes"></textarea></td>
                        </tr>
                        <tr>
                            <td colspan="2"><input type="button" id="btn_add_to_crate" value="Add to Crate" /></td>
                        </tr>
                    </table>`;


    document.getElementById("track_data").innerHTML = html;


}


function button_rec_tool_next_click() {
    recToolCurrentTrackIndex++;


    if (tracks.length > recToolCurrentTrackIndex)
        //update cover-images
        if (recToolCurrentTrackIndex == 0) {
            document.getElementById("img_previous").src = "";
            document.getElementById("img_current").src = URL.createObjectURL(tracks[0].coverArt);
            document.getElementById("img_next").src = URL.createObjectURL(tracks[1].coverArt);
            document.getElementById("btn_rec_tool_previous").disabled = false;

        }
        else if (recToolCurrentTrackIndex > 0 && tracks.length > recToolCurrentTrackIndex + 1) {

            document.getElementById("img_previous").src = URL.createObjectURL(tracks[recToolCurrentTrackIndex - 1].coverArt);
            document.getElementById("img_current").src = URL.createObjectURL(tracks[recToolCurrentTrackIndex].coverArt);
            document.getElementById("img_next").src = URL.createObjectURL(tracks[recToolCurrentTrackIndex + 1].coverArt);
            document.getElementById("btn_rec_tool_previous").disabled = false;

        }
        else { // last track as current

            document.getElementById("img_previous").src = URL.createObjectURL(tracks[recToolCurrentTrackIndex - 1].coverArt);
            document.getElementById("img_current").src = URL.createObjectURL(tracks[recToolCurrentTrackIndex].coverArt);
            document.getElementById("img_next").src = "";
            document.getElementById("btn_rec_tool_next").disabled = true;
            document.getElementById("btn_rec_tool_previous").disabled = false;
        }


    //update rec_tool_audio_player
    document.getElementById("rec_tool_audio_player_src").src = URL.createObjectURL(tracks[recToolCurrentTrackIndex].trackAudio);
    document.getElementById("rec_tool_audio_player").load();
    document.getElementById("rec_tool_audio_player").currentTime = tracks[recToolCurrentTrackIndex].startingPosOfTrack;
    currentTrackAudioFileStartTime = tracks[recToolCurrentTrackIndex].startingPosOfTrack;



    //update track_stack
    document.getElementById("track_stack").innerHTML = "";
    var track_stack_inner_html = "";
    for (var i = 0; i < tracks.length; i++) {
        if (i == recToolCurrentTrackIndex) {

            track_stack_inner_html += `<div class='track_stack_item_red'>${tracks[i].trackTitle}</div>`;
        }
        else {
            track_stack_inner_html += `<div class='track_stack_item'>${tracks[i].trackTitle}</div>`;
        }
    }
    document.getElementById("track_stack").innerHTML = track_stack_inner_html;
}

function btn_rec_tool_previous_click() {

    recToolCurrentTrackIndex--;


    if (tracks.length > recToolCurrentTrackIndex)
        //update cover-images
        if (recToolCurrentTrackIndex == 0) {
            document.getElementById("img_previous").src = "";
            document.getElementById("img_current").src = URL.createObjectURL(tracks[0].coverArt);
            document.getElementById("img_next").src = URL.createObjectURL(tracks[1].coverArt);
            document.getElementById("btn_rec_tool_previous").disabled = true;
            document.getElementById("btn_rec_tool_next").disabled = false;
        }
        else if (recToolCurrentTrackIndex > 0 && tracks.length > recToolCurrentTrackIndex + 1) {

            document.getElementById("img_previous").src = URL.createObjectURL(tracks[recToolCurrentTrackIndex - 1].coverArt);
            document.getElementById("img_current").src = URL.createObjectURL(tracks[recToolCurrentTrackIndex].coverArt);
            document.getElementById("img_next").src = URL.createObjectURL(tracks[recToolCurrentTrackIndex + 1].coverArt);
            document.getElementById("btn_rec_tool_next").disabled = false;
        }
        else { // last track as current

            document.getElementById("img_previous").src = URL.createObjectURL(tracks[recToolCurrentTrackIndex - 1].coverArt);
            document.getElementById("img_current").src = URL.createObjectURL(tracks[recToolCurrentTrackIndex].coverArt);
            document.getElementById("img_next").src = "";
            document.getElementById("btn_rec_tool_next").disabled = false;
        }


    //update rec_tool_audio_player
    //update rec_tool_audio_player
    document.getElementById("rec_tool_audio_player_src").src = URL.createObjectURL(tracks[recToolCurrentTrackIndex].trackAudio);
    document.getElementById("rec_tool_audio_player").load();
    document.getElementById("rec_tool_audio_player").currentTime = tracks[recToolCurrentTrackIndex].startingPosOfTrack;
    currentTrackAudioFileStartTime = tracks[recToolCurrentTrackIndex].startingPosOfTrack;

    //update track_stack
    document.getElementById("track_stack").innerHTML = "";
    var track_stack_inner_html = "";
    for (var i = 0; i < tracks.length; i++) {
        if (i == recToolCurrentTrackIndex) {

            track_stack_inner_html += `<div class='track_stack_item_red'>${tracks[i].trackTitle}</div>`;
        }
        else {
            track_stack_inner_html += `<div class='track_stack_item'>${tracks[i].trackTitle}</div>`;
        }
    }
    document.getElementById("track_stack").innerHTML = track_stack_inner_html;
    //else
}

function btn_save_details_click() {

    details = new Details();
    details.title = document.getElementById("txt_title").value;
    details.introDescription = document.getElementById("txt_intro_description").value;
    details.trackList = document.getElementById("txt_track_list").value;
    details.OutroDescription = document.getElementById("txt_outr_description").value;
}

async function init_click() {

    const audioStream = await navigator.mediaDevices.getUserMedia({
        audio: true
    });
    const audioContext = new AudioContext();
    const audioSource = audioContext.createMediaStreamSource(audioStream);
    const analyser = audioContext.createAnalyser();
    analyser.fftSize = 512;
    analyser.minDecibels = -127;
    analyser.maxDecibels = 0;
    analyser.smoothingTimeConstant = 0.4;
    audioSource.connect(analyser);
    const volumes = new Uint8Array(analyser.frequencyBinCount);
    volumeCallback = () => {
        analyser.getByteFrequencyData(volumes);
        let volumeSum = 0;
        for (const volume of volumes)
            volumeSum += volume;
        const averageVolume = volumeSum / volumes.length;
        // Value range: 127 = analyser.maxDecibels - analyser.minDecibels;
        volumeVisualizer.style.setProperty('--volume', (averageVolume * 100 / 127) + '%');
        //
        if (rec_tool_mic_visualizing == true) {

            document.getElementById("rec-tool-volume-visualizer").style.setProperty('--volume', (averageVolume * 100 / 127) + '%');
        }
    };
}

function start_click() {

    // Updating every 100ms (should be same as CSS transition speed)
    if (volumeCallback !== null && volumeInterval === null)
        volumeInterval = setInterval(volumeCallback, 100);
}