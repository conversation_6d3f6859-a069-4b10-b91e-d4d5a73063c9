﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="WebForm1.aspx.cs" Inherits="studio.WebForm1" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
    <link rel="stylesheet" href="/Content/studio.css">
</head>
<body>
    <table>
        <tr>
            <td style="margin: 10px">
                <div name="sidebar">
                    <div>
                        <input id="btn_create" type="button" onclick="menuButtonClicked(this)" value="Crate" />
                    </div>
                    <div>
                        <input id="btn_settings" type="button" onclick="menuButtonClicked(this)" value="settings" />
                    </div>
                    <div>
                        <input id="btn_details" type="button" onclick="menuButtonClicked(this)" value="details" />
                    </div>
                    <div>
                        <input id="btn_rec_tool" type="button" onclick="menuButtonClicked(this)" value="Rec Tool" />
                    </div>
                </div>
            </td>
            <td style="margin: 10px">
                <div name="content">
                    <div id="crate">
                        <div>Build Crate</div>
                        <div id="div_upload_track">
                            <input id='upload_track_audio' type='file' style='display: none' accept='.mp3' />
                            <div>
                                <input type="button" value="Upload Track" style="height: 20px; width: 100px" onclick='upload_track_audio.click()' />
                            </div>
                        </div>

                        <div id="track_list">
                        </div>

                        <div id="track_data">
                        </div>

                    </div>
                    <div id="settings">
                        <label>Camara Source</label><br />
                        <video id="video_settings" style="border: 2px solid" width="320" height="240" autoplay></video>
                        <br />
                        <input type="button" id="btn_start_camera" value="Preview" />

                        <br />
                        <br />


                        <label>Audio Source</label>
                        <div id="volume-visualizer"></div>
                        <button id="init">init</button>
                        <button id="start">Start</button>
                        <button id="stop">Stop</button>



                    </div>
                    <div id="details">
                        <table>
                            <tr>
                                <td>Details about the dig</td>
                            </tr>
                            <tr>
                                <td>Title<br />
                                    <input id="txt_title" type="text" style="width: 100%" />
                                </td>
                            </tr>
                            <tr>
                                <td>Intro Description<br />
                                    <textarea id="txt_intro_description"></textarea>
                                </td>
                            </tr>
                            <tr>
                                <td>Track List<br />
                                    <textarea id="txt_track_list"></textarea>
                                </td>
                            </tr>
                            <tr>
                                <td>Outro Description<br />
                                    <textarea id="txt_outr_description"></textarea>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input type="button" id="btn_save_details" value="Save" />
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div id="rec-tool">
                        <div>
                            <div name="cover-images">
                                <image id="img_previous" style="width: 100px; height: 100px; border: solid">
                                </image>
                                <image id="img_current" style="width: 100px; height: 100px; border: solid; border-color: red">
                                </image>
                                <image id="img_next" style="width: 100px; height: 100px; border: solid">
                                </image>
                            </div>
                            <div>
                                <audio id="rec_tool_audio_player" controls>
                                    <source src="" id="rec_tool_audio_player_src" />
                                </audio>
                                <input type="button" style="width: 120px; height: 30px" value="Previous" id="btn_rec_tool_previous" />
                                <input type="button" style="width: 120px; height: 30px" value="Next" id="btn_rec_tool_next" />
                            </div>
                        </div>
                        <div name="track_stack" id="track_stack" style="overflow-y: scroll; height: 200px;">
                        </div>

                        <div>
                            <div>
                                <br />
                                <br />
                                <div style="width: 150px; height: 120px; border: solid;">
                                </div>
                                <div style="width: 150px; height: 120px; border: solid;">
                                    <video id="rec_tool_cam_video" style="border: 2px solid; width: 100%; height: 100%" autoplay></video>
                                </div>
                            </div>

                            <div>
                                Mic
                                <div id="rec-tool-volume-visualizer"></div>

                            </div>
                            Track
                            <div >
                                <canvas id="canvas"></canvas>
                            </div>
                            <div>
                            </div>

                            <div>
                                <input type="button" id="btn_rec_tool_recording" value="Start Recording" style="width: 100%" />
                            </div>
                        </div>

                    </div>
                </div>
            </td>
        </tr>
    </table>



    <script src="Scripts/classes/Track.js"></script>
    <script src="Scripts/classes/Details.js"></script>
    <script src="Scripts/functions.js"></script>

    <script>


        //temp variables
        var rec_tool_mic_visualizing = true;


        var tracks = [];
        var trackCount = 0;
        var details;

        var currentTrackAudioFile;
        var currentTrackAudioFileStartTime;
        var currentTrackCoverFile;

        let volumeCallback = null;
        let volumeInterval = null;
        const volumeVisualizer = document.getElementById('volume-visualizer');



        var recToolCurrentTrackIndex;




        hideAllMainDivs();
        document.getElementById("crate").style.display = "block";

        


       



        document.getElementById("btn_rec_tool_next").addEventListener('click', button_rec_tool_next_click);


        document.getElementById("btn_rec_tool_previous").addEventListener('click', btn_rec_tool_previous_click);

        document.getElementById("btn_save_details").addEventListener('click', btn_save_details_click);

        document.getElementById("init").addEventListener('click', init_click);

        document.getElementById("start").addEventListener('click', start_click);


        let camera_button = document.getElementById("btn_start_camera");
        let camara_video = document.getElementById("video_settings");
        let rec_tool_cam_video = document.getElementById("rec_tool_cam_video");

        camera_button.addEventListener('click', async function () {
            camera_stream = await navigator.mediaDevices.getUserMedia({ video: true });
            camara_video.srcObject = camera_stream;
            rec_tool_cam_video.srcObject = camera_stream;
        });


        document.getElementById("upload_track_audio").addEventListener("change", upload_track_audio_eventhandler, false);


       


    </script>
</body>
</html>
