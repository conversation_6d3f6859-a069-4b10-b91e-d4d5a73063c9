﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="*******" targetFramework="net452" />
  <package id="AspNet.ScriptManager.bootstrap" version="3.4.1" targetFramework="net452" />
  <package id="AspNet.ScriptManager.jQuery" version="3.4.1" targetFramework="net452" />
  <package id="bootstrap" version="3.4.1" targetFramework="net452" />
  <package id="jQuery" version="3.4.1" targetFramework="net452" />
  <package id="Microsoft.AspNet.FriendlyUrls" version="1.0.2" targetFramework="net452" />
  <package id="Microsoft.AspNet.FriendlyUrls.Core" version="1.0.2" targetFramework="net452" />
  <package id="Microsoft.AspNet.ScriptManager.MSAjax" version="5.0.0" targetFramework="net452" />
  <package id="Microsoft.AspNet.ScriptManager.WebForms" version="5.0.0" targetFramework="net452" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.Web.Optimization.WebForms" version="1.1.3" targetFramework="net452" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.1" targetFramework="net452" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net452" />
  <package id="Modernizr" version="2.8.3" targetFramework="net452" />
  <package id="Newtonsoft.Json" version="12.0.2" targetFramework="net452" />
  <package id="WebGrease" version="1.6.0" targetFramework="net452" />
</packages>